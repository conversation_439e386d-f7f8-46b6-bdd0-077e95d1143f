// 自定义 Element Plus 样式

// 卡片
.el-card {
  background-color: var(--el-bg-color) !important;
}

// 分页
.el-pagination {
  // 参考 Bootstrap 的响应式设计 WIDTH = 768
  @media screen and (max-width: 768px) {
    .el-pagination__total,
    .el-pagination__sizes,
    .el-pagination__jump,
    .btn-prev,
    .btn-next {
      display: none;
    }
  }

  .btn-prev {
    margin-right: 4px;
  }

  .btn-next {
    margin-left: 4px;
  }

  .el-pager {
    .number {
      background-color: transparent !important;
      border-radius: 8px !important;
      margin: 0 4px !important;
      border: 1px solid #e2e3e6;

      &.is-active {
        background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%), #0063ff;
        color: #fff !important;
      }
    }

    .more {
      background-color: transparent !important;

      &:hover {
        color: #1a61f7 !important;
      }
    }
  }

  .btn-prev,
  .btn-next {
    background-color: transparent !important;
    border: 1px solid #d1d5db;
    border-radius: 4px;
    height: 30px !important;

    &:hover {
      color: #1a61f7 !important;
      border: 1px solid #1a61f7 !important;
    }
  }
}

// 自定义头部tab
.common-header-tabs {
  .el-tabs__nav-wrap::after {
    display: none;
  }

  .el-tabs__header {
    .el-tabs__item {
      color: #2b2c33;
      font-size: 18px;
      font-weight: bold;
    }

    .el-tabs__active-bar {
      background-color: #0055ff;
      height: 5px;
    }
  }
}

// 自定义卡片tab
.card-tabs {
  .el-tabs__nav.is-stretch > * {
    flex: 0 !important;
  }
  .el-tabs__item.is-active,
  .el-tabs__item:hover {
    cursor: pointer;
    color: #0055ff !important;
    font-size: 14px !important;
  }

  .el-tabs__header .el-tabs__nav {
    border: none !important;
  }

  .el-tabs__header {
    height: 48px !important;
    line-height: 48px !important;
    margin-bottom: 0 !important;
  }

  .el-tabs__nav {
    height: 48px !important;
  }

  .el-tabs__item {
    height: 48px !important;
    line-height: 48px !important;

    &:last-child {
      border-right: 1px solid var(--el-border-color-light) !important;
    }
  }
}
// 自定义按钮
.common-button-1 {
  width: auto;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 8px !important;
  color: #6d6f75 !important;
  border: 1px solid #0055ff !important;
  background-color: #fff !important;
  font-size: 16px !important;

  &:hover {
    color: #0055ff !important;
    border: 1px solid #0055ff !important;
    background-color: rgba($color: #0055ff, $alpha: 0.1) !important;
  }
}

.common-button-2 {
  width: auto;
  height: 48px !important;
  line-height: 48px !important;
  border-radius: 8px !important;
  color: #fff !important;
  border: none !important;
  background-color: #0055ff !important;
  font-size: 16px;

  &:hover {
    color: #fff !important;
    background-color: #0055ff !important;
    opacity: 0.7;
  }
}

.common-button-3 {
  width: 120px;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 20px !important;
  color: #fff !important;
  border: none !important;
  background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
  font-size: 16px;

  &:hover {
    color: #fff !important;
    background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
    opacity: 0.7;
  }
}

.common-button-4 {
  width: 120px;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 20px !important;
  color: #239dde !important;
  border: 1px solid #239dde !important;
  background: #fff;
  font-size: 16px;

  &:hover {
    color: #239dde !important;
    background: #239dde;
    opacity: 0.7;
  }
}

.common-button-5 {
  width: 120px;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 8px !important;
  color: #fff !important;
  border: none !important;
  background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
  font-size: 16px;

  &:hover {
    color: #fff !important;
    background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
    opacity: 0.7;
  }
}

.common-button-6 {
  width: auto;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 8px !important;
  color: #4b5563 !important;
  border: 1px solid #d1d5db !important;
  background-color: #fff !important;
  font-size: 16px !important;

  &:hover {
    color: #4b5563 !important;
    border: 1px solid #d1d5db !important;
    background-color: rgba($color: #4b5563, $alpha: 0.1) !important;
  }
}

.common-button-7 {
  width: auto;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 8px !important;
  color: #fff !important;
  border: none !important;
  background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
  font-size: 16px;

  &:hover {
    color: #fff !important;
    background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
    opacity: 0.7;
  }
}

.reset-button {
  width: 120px;
  height: 40px !important;
  line-height: 40px !important;
  border-radius: 20px !important;
  color: #239dde !important;
  border: none !important;
  background-color: #f1f9ff !important;
  font-size: 16px;

  &:hover {
    color: #fff !important;
    background-color: #f1f9ff !important;
    opacity: 0.7;
  }
}

// 自定义搜索表单
.search-form {
  .el-form-item {
    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-select {
      max-width: 328px !important;

      &__wrapper {
        max-width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }
  }
}

.search-form-2 {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  column-gap: 20px;
  row-gap: 20px;

  .el-form-item {
    margin-bottom: 0;

    &__content {
      height: 48px;
    }

    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 100% !important;

      &__wrapper {
        width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-date-editor {
      height: 48px !important;
      line-height: 48px !important;
    }
  }

  // 第一行第一个元素占两列
  .el-form-item:nth-child(1) {
    grid-column: 1 / span 2;
  }
  // 第二行最后一个元素占两列（假设每行4个item，第二行第4个是第8个）
  .el-form-item:nth-child(8) {
    grid-column: 4 / 6;
    justify-self: end;
    align-self: end;
  }

  .hidden-label {
    .el-form-item__label {
      visibility: hidden;
    }
  }
}

.search-form-3 {
  display: flex;
  justify-content: space-between;
  align-items: end;
  gap: 20px;

  .form-left {
    display: flex;
    align-items: end;
    gap: 20px;
    flex: 1;
  }

  .form-right {
    display: flex;
    align-items: end;
    gap: 12px;
  }

  .el-form-item {
    margin-bottom: 0;

    &__content {
      height: 48px;
    }

    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 240px !important;

      &__wrapper {
        width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-date-editor {
      height: 48px !important;
      line-height: 48px !important;
    }
  }

  .hidden-label {
    .el-form-item__label {
      visibility: hidden;
    }
  }
}

.search-form-4 {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  column-gap: 20px;
  row-gap: 20px;

  .el-form-item {
    margin-bottom: 0;

    &__content {
      height: 48px;
    }

    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 100% !important;

      &__wrapper {
        width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-date-editor {
      height: 48px !important;
      line-height: 48px !important;
    }
  }

  // 按钮行样式 - 占据整行并右对齐
  .button-row {
    grid-column: 1 / -1; // 占据整行
    display: flex;
    justify-content: flex-end; // 右对齐
    gap: 16px;
    margin-top: 8px;
  }

  .hidden-label {
    .el-form-item__label {
      visibility: hidden;
    }
  }
}

.search-form-5 {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  column-gap: 20px;
  row-gap: 20px;

  .el-form-item {
    margin-bottom: 0;

    &__content {
      height: 48px;
    }

    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 100% !important;

      &__wrapper {
        width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-date-editor {
      height: 48px !important;
      line-height: 48px !important;
    }
  }

  // 第一行第一个元素占两列
  .el-form-item:nth-child(1) {
    grid-column: 1 / span 2;
  }

  // 第二行后三列合并（第8、9、10个元素）

  // 第二行最后一个元素占两列（假设每行4个item，第二行第4个是第9个）
  .el-form-item:nth-child(9) {
    grid-column: 4 / 7;
    justify-self: end;
    align-self: end;
  }

  // 按钮行样式 - 占据整行并右对齐
  .button-row {
    grid-column: 1 / -1; // 占据整行
    display: flex;
    justify-content: flex-end; // 右对齐
    gap: 16px;
    margin-top: 8px;
  }

  .hidden-label {
    .el-form-item__label {
      visibility: hidden;
    }
  }
}

// 自定义表格
.custom-table {
  border-radius: 8px !important;
  border: 1px solid #e2e3e6 !important;
  border-bottom: none !important;

  .el-table__header {
    .el-table__cell {
      position: relative;
      height: 53px !important;
      font-size: 16px !important;
      color: #2b2c33;
      font-weight: 600;

      &::after {
        position: absolute;
        content: " ";
        right: 0;
        top: 10px;
        width: 1px;
        height: 32px;
        background-color: #e2e3e6;
      }

      &:last-child {
        &::after {
          display: none;
        }
      }
    }

    &-wrapper {
      th {
        background-color: #fff !important;
      }
    }
  }

  .el-table__body {
    .el-table__row {
      background-color: #f8fafb;

      .el-table__cell {
        padding: 12px 0 !important;
        font-size: 16px !important;
        background-color: #f8fafb;
      }

      &--striped {
        background-color: #fff;

        .el-table__cell {
          background: #fff !important;
        }
      }

      &.hover-row td {
        background-color: #ecf2ff !important;
      }
    }
  }
}

.custom-table-2 {
  border-top: 1px solid #e2e3e6 !important;
  border-bottom: none !important;

  .el-table__header {
    .el-table__cell {
      position: relative;
      height: 53px !important;
      font-size: 16px !important;
      color: #2b2c33;
      font-weight: 600;
      background-color: #f9fafb !important;

      // &::after {
      //   position: absolute;
      //   content: " ";
      //   right: 0;
      //   top: 10px;
      //   width: 1px;
      //   height: 32px;
      //   background-color: #e2e3e6;
      // }

      // &:last-child {
      //   &::after {
      //     display: none;
      //   }
      // }
    }

    &-wrapper {
      th {
        background-color: #fff !important;
      }
    }
  }

  .el-table__body {
    .el-table__row {
      .el-table__cell {
        padding: 12px 0 !important;
        font-size: 16px !important;
      }

      &.hover-row td {
        background-color: #ecf2ff !important;
      }
    }
  }
}

// 自定义确认框
.custom-confirm-box {
  padding: 20px;

  .el-message-box__header {
    padding-bottom: 20px;

    .el-message-box__title {
      font-size: 18px;
      line-height: 26px;
      font-weight: 600;
      color: #31333e;
    }

    .el-message-box__headerbtn {
      width: 56px;
      height: 56px;

      &:hover {
        color: var(--blue1) !important;
      }
    }
  }

  .el-message-box__container {
    .el-message-box__message {
      font-size: 16px;
    }
  }

  .el-message-box__btns {
    display: flex;
    justify-content: flex-end;
    padding-top: 30px;

    .confirm-btn {
      width: 112px;
      height: 44px;
      border: none !important;
      background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
      box-shadow: 0px 4px 6px 0px rgba(0, 85, 255, 0.24);
      border-radius: 8px;
      color: #fff;
      font-size: 16px;
      line-height: 24px;

      &:hover {
        background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
        color: #fff;
        opacity: 0.7;
      }

      &:disabled {
        opacity: 0.3;
        background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
        color: #fff;
      }
    }

    .cancel-btn {
      width: 112px;
      height: 44px;
      background: #fff;
      border-radius: 8px;
      border: 1px solid #239DDE;
      color: #61636e;
      font-size: 16px;
      line-height: 24px;
    }
  }
}

// 自定义弹窗
.custom-dialog {
  padding: 20px;
  padding-bottom: 32px;

  .el-dialog__header > .el-dialog__title {
    --el-text-color-primary: var(--grey1);
    font-weight: 600;
    font-size: 18px;
  }

  .el-dialog__headerbtn {
    width: 50px;
    height: 55px;
    font-size: 16px;
  }

  .el-dialog__body {
    .el-form-item__label {
      color: var(--grey1);
      font-size: 16px;
      font-weight: 600;
      line-height: 24px;

      .tip-container {
        position: relative;

        .tip {
          position: absolute;
          left: 80px;
          top: 0;
          width: 200px;
        }
      }
    }

    .el-form-item.is-required {
      .el-form-item__label {
        &::after {
          color: var(--el-color-danger);
          content: "*";
          margin-left: 4px;
        }

        &::before {
          display: none;
        }
      }
    }

    .download-form {
      .el-form-item__label {
        line-height: 24px;
      }
    }
  }

  .el-form-item {
    .el-input__wrapper {
      height: 48px;
      line-height: 48px;
      border-radius: 8px;
    }

    .el-input__inner {
      height: 46px;
      line-height: 46px;
      font-size: 16px;
    }

    .el-date-editor {
      width: 100%;
      height: 48px;
      border-radius: 8px;
    }

    .el-select {
      height: 48px;
      border-radius: 8px;

      &__wrapper {
        height: 48px;
        font-size: 16px;
        border-radius: 8px;
      }
    }

    .el-upload {
      width: 100%;

      .el-input__inner {
        cursor: pointer !important;
      }
    }

    .upload-has-file {
      position: relative;

      .el-input__wrapper {
        padding-right: 34px;
      }

      .upload-close-icon {
        position: absolute;
        right: 14px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
      }
    }
  }

  .confirm-btn {
    width: 78px;
    height: 40px !important;
    line-height: 40px !important;
    border-radius: 8px !important;
    color: #fff !important;
    border: none !important;
    background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
    font-size: 16px;

    &:hover {
      color: #fff !important;
      background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
      opacity: 0.7;
    }

    &:disabled {
      opacity: 0.3;
      background: linear-gradient(270deg, #0ec3ed 0%, #239dde 100%);
      color: #fff;
    }
  }

  .cancel-btn {
    width: 78px;
    height: 40px !important;
    line-height: 40px !important;
    border-radius: 8px !important;
    color: #4b5563 !important;
    border: 1px solid #d1d5db !important;
    background-color: #fff !important;
    font-size: 16px !important;

    &:hover {
      color: #4b5563 !important;
      border: 1px solid #d1d5db !important;
      background-color: rgba($color: #4b5563, $alpha: 0.1) !important;
    }
  }

  .export-footer {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.el-form-item.is-required {
  .el-form-item__label {
    &::after {
      color: var(--el-color-danger);
      content: "*";
      margin-left: 4px;
    }

    &::before {
      display: none;
    }
  }
}

// 自定义表单
.custom-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
  column-gap: 80px;
  row-gap: 20px;

  .el-form-item {
    margin-bottom: 0;

    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 100% !important;

      &__wrapper {
        max-width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .date-picker-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 48px;
      width: 100%;

      .el-date-editor {
        width: 100%;
        height: 48px;
        border-radius: 8px;
      }
    }
  }
}

.custom-form-2 {
  .el-form-item {
    &__label {
      color: #2b2c33;
      font-size: 16px;
      line-height: 48px;
      font-weight: bold;
    }

    .el-input {
      &__wrapper {
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .el-select {
      width: 100% !important;

      &__wrapper {
        max-width: 100% !important;
        height: 48px !important;
        line-height: 48px !important;
        font-size: 16px;
      }
    }

    .date-picker-wrapper {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 48px;
      width: 100%;

      .el-date-editor {
        width: 100%;
        height: 48px;
        border-radius: 8px;
      }
    }

    .evaluation-standards-container {
      width: 100%;
    }
  }
}

.evaluation-standard-popover {
  padding: 16px !important;

  .standard-popup-content {
    .standard-popup-title {
      font-size: 16px;
      font-weight: 500;
      color: #fff;
      margin-bottom: 12px;
      padding-bottom: 8px;
      display: flex;
      align-items: center;

      .title-dot {
        color: #ffab00;
        margin-right: 6px;
        font-size: 12px;
      }
    }

    .standard-items {
      .standard-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;

        &:last-child {
          margin-bottom: 0;
        }

        .standard-item-index {
          display: flex;
          justify-content: center;
          align-items: center;
          color: #fff;
          font-size: 14px;
          margin-right: 4px;
          flex-shrink: 0;
        }

        .standard-item-content {
          font-size: 14px;
          color: #fff;
        }
      }
    }
  }
}

// 通知面板样式
.notification-popover {
  padding: 0 !important;

  .notification-panel {
    .notification-header {
      padding: 12px 14px;

      .notification-title {
        font-size: 16px;
        font-weight: 600;
        color: #2B2C33;
      }
    }

    .notification-list {
      max-height: 300px;
      overflow-y: auto;

      .notification-item {
        display: flex;
        justify-content: flex-start;
        align-items: flex-start;
        padding: 7px 14px;
        cursor: pointer;
        position: relative;
        transition: background-color 0.3s;

        &:hover {
          background-color: #ECF2FF;
        }

        .notification-content {
          display: flex;
          justify-content: flex-start;
          align-items: flex-start;
          flex: 1;

          .notification-text-container {
            flex: 1;
            padding-left: 7px;
          }

          .notification-text {
            font-size: 14px;
            color: #2B2C33;
            margin-bottom: 4px;
          }

          .notification-time {
            font-size: 12px;
            color: #999;
          }
        }
      }

      .no-notifications {
        padding: 40px 20px;
        text-align: center;
        color: #999;
        font-size: 14px;
      }
    }
  }
}
