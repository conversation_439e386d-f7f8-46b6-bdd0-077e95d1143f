<script setup>
import { EchartsUI, useEcharts } from "@/components/Echart"

const props = defineProps({
  title: {
    type: String,
    default: "柱状图"
  },
  chartData: {
    type: Array,
    default: () => []
  },
  height: {
    type: String,
    default: "300px"
  },
  colors: {
    type: Array,
    default: () => ['#5470c6']
  }
})

const chartRef = ref()
const { renderEcharts } = useEcharts(chartRef)

watch(
  () => props.chartData,
  () => {
    paintChart()
  },
  { deep: true }
)

onMounted(() => {
  paintChart()
})

function paintChart() {
  if (!props.chartData || props.chartData.length === 0) return

  // 提取类别名称和数值，并为每个数据项添加颜色
  const categories = props.chartData.map(item => item.name)
  const seriesData = props.chartData.map((item, index) => ({
    value: item.value,
    itemStyle: {
      // color: props.colors[index % props.colors.length]
      color: props.colors[0]
    }
  }))

  renderEcharts({
    backgroundColor: "#fff",
    color: props.colors,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function(params) {
        return `${params[0].name}: ${params[0].value}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '3%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#f0f0f0',
          type: 'solid'
        }
      }
    },
    yAxis: {
      type: 'category',
      data: categories,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        fontSize: 12,
        margin: 10
      }
    },
    series: [
      {
        name: props.title,
        type: 'bar',
        data: seriesData,
        barWidth: '60%',
        itemStyle: {
          borderRadius: [0, 4, 4, 0]
        },
        label: {
          show: true,
          position: 'right',
          color: '#666',
          fontSize: 12,
          formatter: '{c}'
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  })
}
</script>

<template>
  <div class="bar-chart-container">
    <EchartsUI ref="chartRef" :height="props.height" />
  </div>
</template>

<style scoped>
.bar-chart-container {
  width: 100%;
}
</style>
