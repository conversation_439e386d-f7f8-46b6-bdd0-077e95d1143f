import { request } from "@/utils/axios"

/** 获取通知列表 */
export function getNotificationListApi(params) {
  return request({
    url: "message",
    method: "get",
    params,
  });
}

/** 标记通知为已读 */
export function markNotificationReadApi(id) {
  return request({
    url: `message/read/${id}`,
    method: "post",
  });
}

/** 标记所有通知为已读 */
export function markAllNotificationsReadApi() {
  return request({
    url: "message/readAll",
    method: "post",
  });
}

/** 修改密码 */
export function changePasswordApi(data) {
  return request({
    url: "auth/change-password",
    method: "post",
    data
  })
}
