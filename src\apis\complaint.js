import { request } from "@/utils/axios";

/* 分办列表 */
export function getComplaintDistributionListApi(params) {
  return request({
    url: "complaint/distribute",
    method: "get",
    params,
  });
}

/* 市级分办 */
export function cityDistributionApi(data) {
  return request({
    url: "complaint/distribute",
    method: "post",
    data,
  });
}

/* 投诉回复 */
export function complaintReplyApi(data) {
  return request({
    url: "complaint/reply",
    method: "post",
    data,
  });
}

/* 投诉审核 */
export function complaintReviewApi(data) {
  return request({
    url: "complaint/approve",
    method: "post",
    data,
  });
}

/* 管理端详情 */
export function complaintDetailApi(complaintId) {
  return request({
    url: `complaint/detail/${complaintId}`,
    method: "get",
  });
}

/* 督办 */
export function addSuperviseApi(data) {
  return request({
    url: "complaint/supervise",
    method: "post",
    data,
  });
}

// 投诉列表
export function getComplaintListApi(params) {
  return request({
    url: "complaint",
    method: "get",
    params,
  });
}

// 督办列表
export function getSuperviseListApi(params) {
  return request({
    url: "complaint/supervise",
    method: "get",
    params,
  });
}


// 申请延期
export function applyDelayApi(data) {
  return request({
    url: "complaint/delay",
    method: "post",
    data,
  });
}

// 延期详情
export function getDelayDetailApi(complainId) {
  return request({
    url: `complaint/delay/detail/${complainId}`,
    method: "get",
  });
}
